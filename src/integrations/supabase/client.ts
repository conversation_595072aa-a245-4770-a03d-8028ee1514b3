// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://keflusixfrrfgaqhnajo.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtlZmx1c2l4ZnJyZmdhcWhuYWpvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQzNDc3NjksImV4cCI6MjA2OTkyMzc2OX0.3KxsRONbnpHun4KbQlhpFWHOYjboyiJOoSm9vieTXJo";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});