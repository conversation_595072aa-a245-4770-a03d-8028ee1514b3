@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 220 20% 97%;
    --foreground: 220 15% 12%;

    --card: 0 0% 100%;
    --card-foreground: 220 15% 12%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 15% 12%;

    --primary: 262 83% 58%;
    --primary-foreground: 0 0% 100%;

    --secondary: 220 14% 96%;
    --secondary-foreground: 220 15% 25%;

    --muted: 220 14% 96%;
    --muted-foreground: 220 9% 46%;

    --accent: 262 83% 58%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 262 83% 58%;

    --radius: 0.75rem;

    /* Beautiful gradient colors */
    --gradient-primary: linear-gradient(135deg, hsl(262 83% 58%), hsl(252 56% 57%));
    --gradient-secondary: linear-gradient(135deg, hsl(220 14% 96%), hsl(210 20% 98%));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(220 14% 98%));
    
    /* Enhanced card colors */
    --card-hover: 220 14% 98%;
    --card-border: 220 13% 91%;
    --card-shadow: 220 13% 91% / 0.1;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 220 20% 8%;
    --foreground: 220 15% 95%;

    --card: 220 20% 10%;
    --card-foreground: 220 15% 95%;

    --popover: 220 20% 10%;
    --popover-foreground: 220 15% 95%;

    --primary: 262 83% 58%;
    --primary-foreground: 0 0% 100%;

    --secondary: 220 20% 15%;
    --secondary-foreground: 220 15% 85%;

    --muted: 220 20% 15%;
    --muted-foreground: 220 9% 65%;

    --accent: 262 83% 58%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 20% 20%;
    --input: 220 20% 20%;
    --ring: 262 83% 58%;

    /* Dark mode gradients */
    --gradient-primary: linear-gradient(135deg, hsl(262 83% 58%), hsl(252 56% 57%));
    --gradient-secondary: linear-gradient(135deg, hsl(220 20% 15%), hsl(220 20% 18%));
    --gradient-card: linear-gradient(145deg, hsl(220 20% 10%), hsl(220 20% 12%));
    
    /* Enhanced dark card colors */
    --card-hover: 220 20% 12%;
    --card-border: 220 20% 20%;
    --card-shadow: 0 0 0 / 0.2;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    background: linear-gradient(135deg, hsl(var(--background)), hsl(var(--muted)));
  }
}

@layer components {
  .card-enhanced {
    @apply bg-card border border-border/50 shadow-lg hover:shadow-xl transition-all duration-300;
    background: var(--gradient-card);
    backdrop-filter: blur(10px);
  }
  
  .card-enhanced:hover {
    @apply -translate-y-1 scale-[1.02];
    box-shadow: 0 20px 40px hsl(var(--card-shadow));
  }
  
  .gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .gradient-border {
    border: 2px solid transparent;
    background: var(--gradient-primary) border-box;
    -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: exclude;
    mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
  }
}